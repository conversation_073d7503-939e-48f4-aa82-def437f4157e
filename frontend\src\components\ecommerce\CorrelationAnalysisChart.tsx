import { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { ApexOptions } from "apexcharts";
import axios from "axios";

interface CorrelationData {
  correlation: number;
  p_value: number | null;
  data_points: number;
  significance: string;
}

interface AlignedDataPoint {
  date: string;
  fed_rate: number;
  btc_price: number;
}

interface FedFundsData {
  date: string;
  rate: number;
}

interface BitcoinData {
  date: string;
  price: number;
  timestamp: number;
}

interface CorrelationResponse {
  correlation_analysis: CorrelationData | null;
  fed_funds_data: FedFundsData[];
  bitcoin_data: BitcoinData[];
  aligned_data: AlignedDataPoint[];
  summary: {
    total_fed_points: number;
    total_btc_points: number;
    aligned_points: number;
    analysis_period_days: number;
    latest_fed_rate: number | null;
    latest_btc_price: number | null;
  };
}

export default function CorrelationAnalysisChart() {
  const [correlationData, setCorrelationData] = useState<CorrelationResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(365); // Default to 1 year

  const periodOptions = [
    { value: 90, label: "3 Months" },
    { value: 180, label: "6 Months" },
    { value: 365, label: "1 Year" },
    { value: 730, label: "2 Years" },
    { value: 1095, label: "3 Years" },
    { value: 1825, label: "5 Years" }
  ];

  useEffect(() => {
    fetchCorrelationData();
  }, [selectedPeriod]);

  const fetchCorrelationData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`http://localhost:8000/api/correlation-analysis/?days=${selectedPeriod}`);
      setCorrelationData(response.data);
    } catch (err: any) {
      console.error("Error fetching correlation data:", err);
      setError(err.response?.data?.error || "Failed to fetch correlation data");
    } finally {
      setIsLoading(false);
    }
  };

  const getCorrelationColor = (correlation: number | null): string => {
    if (correlation === null) return "#6B7280";
    if (correlation > 0.3) return "#10B981"; // Strong positive - green
    if (correlation > 0.1) return "#F59E0B"; // Weak positive - yellow
    if (correlation > -0.1) return "#6B7280"; // Neutral - gray
    if (correlation > -0.3) return "#F97316"; // Weak negative - orange
    return "#EF4444"; // Strong negative - red
  };

  const getCorrelationDescription = (correlation: number | null): string => {
    if (correlation === null) return "No correlation data";
    if (correlation > 0.7) return "Strong Positive";
    if (correlation > 0.3) return "Moderate Positive";
    if (correlation > 0.1) return "Weak Positive";
    if (correlation > -0.1) return "No Correlation";
    if (correlation > -0.3) return "Weak Negative";
    if (correlation > -0.7) return "Moderate Negative";
    return "Strong Negative";
  };

  // Get date formatting based on selected period
  const getDateFormat = (days: number) => {
    if (days <= 90) return 'dd MMM'; // 3 months: "15 Jan"
    if (days <= 180) return 'dd MMM'; // 6 months: "15 Jan"
    if (days <= 365) return 'MMM yyyy'; // 1 year: "Jan 2024"
    return 'MMM yyyy'; // 2+ years: "Jan 2024"
  };

  const getTickAmount = (days: number) => {
    if (days <= 90) return 6; // Show ~6 ticks for 3 months
    if (days <= 180) return 8; // Show ~8 ticks for 6 months
    if (days <= 365) return 12; // Show ~12 ticks for 1 year
    if (days <= 730) return 12; // Show ~12 ticks for 2 years
    return 10; // Show ~10 ticks for longer periods
  };

  const getPeriodLabel = (days: number) => {
    const option = periodOptions.find(opt => opt.value === days);
    return option ? option.label : `${days} Days`;
  };

  // Prepare chart data
  const chartOptions: ApexOptions = {
    chart: {
      type: 'line',
      height: 400,
      fontFamily: "Outfit, sans-serif",
      toolbar: {
        show: false,
      },
      background: 'transparent',
    },
    title: {
      text: 'Bitcoin Price vs Federal Funds Rate',
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#6B7280'
      }
    },
    stroke: {
      curve: 'smooth',
      width: [3, 2],
    },
    colors: ['#F59E0B', '#3B82F6'], // Bitcoin orange, Fed blue
    xaxis: {
      type: 'datetime',
      labels: {
        style: {
          colors: '#6B7280',
        },
        format: getDateFormat(selectedPeriod),
        datetimeUTC: false,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      tickAmount: getTickAmount(selectedPeriod),
    },
    yaxis: [
      {
        title: {
          text: 'Bitcoin Price (USD)',
          style: {
            color: '#F59E0B',
            fontSize: '12px',
            fontWeight: 'bold',
          }
        },
        labels: {
          style: {
            colors: '#F59E0B',
          },
          formatter: (val: number) => `$${val.toLocaleString()}`
        },
        axisBorder: {
          show: true,
          color: '#F59E0B'
        }
      },
      {
        opposite: true,
        title: {
          text: 'Federal Funds Rate (%)',
          style: {
            color: '#3B82F6',
            fontSize: '12px',
            fontWeight: 'bold',
          }
        },
        labels: {
          style: {
            colors: '#3B82F6',
          },
          formatter: (val: number) => `${val.toFixed(2)}%`
        },
        axisBorder: {
          show: true,
          color: '#3B82F6'
        }
      }
    ],
    legend: {
      show: true,
      position: 'top',
      horizontalAlign: 'right',
    },
    grid: {
      borderColor: '#E5E7EB',
      strokeDashArray: 3,
    },
    tooltip: {
      shared: true,
      intersect: false,
      x: {
        format: selectedPeriod <= 365 ? 'dd MMM yyyy' : 'MMM yyyy'
      },
      y: [
        {
          formatter: (val: number) => `$${val.toLocaleString()}`
        },
        {
          formatter: (val: number) => `${val.toFixed(2)}%`
        }
      ]
    },
    markers: {
      size: 0,
      hover: {
        size: 6,
      }
    }
  };

  const chartSeries = correlationData ? [
    {
      name: 'Bitcoin Price',
      type: 'line',
      yAxisIndex: 0,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.btc_price
      }))
    },
    {
      name: 'Federal Funds Rate',
      type: 'line',
      yAxisIndex: 1,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.fed_rate
      }))
    }
  ] : [];

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      {/* Header */}
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Bitcoin-Federal Funds Rate Correlation Analysis
          </h3>
          <p className="mt-1 text-gray-500 text-theme-sm dark:text-gray-400">
            Statistical correlation between Bitcoin price movements and Federal Reserve interest rates
          </p>
        </div>

        {/* Period Selector */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Period:
          </label>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {periodOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex items-center justify-center h-[400px] text-red-500">
          <div className="text-center">
            <p className="text-lg font-medium">Error Loading Data</p>
            <p className="text-sm mt-2">{error}</p>
            <button
              onClick={fetchCorrelationData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Content */}
      {!isLoading && !error && correlationData && (
        <>
          {/* Correlation Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Correlation Coefficient</div>
              <div
                className="text-lg font-bold"
                style={{ color: getCorrelationColor(correlationData.correlation_analysis?.correlation || null) }}
              >
                {correlationData.correlation_analysis?.correlation?.toFixed(3) || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">
                {getCorrelationDescription(correlationData.correlation_analysis?.correlation || null)}
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Data Points</div>
              <div className="text-lg font-bold text-gray-800 dark:text-white">
                {correlationData.summary.aligned_points}
              </div>
              <div className="text-sm text-gray-600">
                Aligned observations
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Latest Fed Rate</div>
              <div className="text-lg font-bold text-blue-600">
                {correlationData.summary.latest_fed_rate?.toFixed(2)}%
              </div>
              <div className="text-sm text-gray-600">
                Current rate
              </div>
            </div>
          </div>

          {/* Chart */}
          <div className="max-w-full overflow-x-auto custom-scrollbar">
            <div className="min-w-[1000px] xl:min-w-full">
              <Chart
                options={chartOptions}
                series={chartSeries}
                type="line"
                height={400}
              />
            </div>
          </div>

          {/* Analysis Summary */}
          {correlationData.correlation_analysis && (
            <div className="mt-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
                Analysis Summary
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                The correlation coefficient of <strong>{correlationData.correlation_analysis.correlation.toFixed(3)}</strong> indicates a{" "}
                <strong>{getCorrelationDescription(correlationData.correlation_analysis.correlation).toLowerCase()}</strong> relationship
                between Bitcoin prices and Federal Funds rates over the selected {selectedPeriod}-day period.
                {correlationData.correlation_analysis.p_value && (
                  <> The relationship is statistically <strong>{correlationData.correlation_analysis.significance}</strong>
                  (p-value: {correlationData.correlation_analysis.p_value.toFixed(4)}).</>
                )}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
