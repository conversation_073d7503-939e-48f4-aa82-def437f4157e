import { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { ApexOptions } from "apexcharts";
import axios from "axios";

interface HashRateCorrelationData {
  correlation: number;
  p_value: number | null;
  data_points: number;
  significance: string;
}

interface AlignedDataPoint {
  date: string;
  hash_rate: number;
  btc_price: number;
  timestamp: number;
}

interface HashRateData {
  date: string;
  hash_rate: number;
  timestamp: number;
}

interface BitcoinData {
  date: string;
  price: number;
  timestamp: number;
}

interface HashRateCorrelationResponse {
  correlation_analysis: HashRateCorrelationData | null;
  hash_rate_data: HashRateData[];
  bitcoin_data: BitcoinData[];
  aligned_data: AlignedDataPoint[];
  summary: {
    total_hash_points: number;
    total_btc_points: number;
    aligned_points: number;
    analysis_period_days: number;
    latest_hash_rate: number | null;
    latest_btc_price: number | null;
  };
}

const HashRateCorrelationChart: React.FC = () => {
  const [correlationData, setCorrelationData] = useState<HashRateCorrelationResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(365); // Default to 1 year

  const periodOptions = [
    { value: 30, label: "1 Month" },
    { value: 90, label: "3 Months" },
    { value: 180, label: "6 Months" },
    { value: 365, label: "1 Year" },
    { value: 730, label: "2 Years" },
    { value: 1095, label: "3 Years" },
    { value: 1460, label: "4 Years" },
    { value: 1825, label: "5 Years" },
  ];

  const fetchCorrelationData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`http://localhost:8000/api/hash-rate-correlation/?days=${selectedPeriod}`);
      setCorrelationData(response.data);
    } catch (err: any) {
      console.error("Error fetching hash rate correlation data:", err);
      setError(err.response?.data?.error || "Failed to fetch hash rate correlation data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCorrelationData();
  }, [selectedPeriod]);

  const getCorrelationColor = (correlation: number | null): string => {
    if (correlation === null) return "#6B7280";
    if (correlation > 0.7) return "#10B981"; // Strong positive - green
    if (correlation > 0.3) return "#F59E0B"; // Moderate positive - amber
    if (correlation > -0.3) return "#6B7280"; // Weak - gray
    if (correlation > -0.7) return "#F97316"; // Moderate negative - orange
    return "#EF4444"; // Strong negative - red
  };

  const getCorrelationDescription = (correlation: number | null): string => {
    if (correlation === null) return "No correlation";
    if (correlation > 0.7) return "Strong Positive";
    if (correlation > 0.3) return "Moderate Positive";
    if (correlation > -0.3) return "Weak";
    if (correlation > -0.7) return "Moderate Negative";
    return "Strong Negative";
  };

  const formatHashRate = (hashRate: number): string => {
    if (hashRate >= 1e18) return `${(hashRate / 1e18).toFixed(2)} EH/s`;
    if (hashRate >= 1e15) return `${(hashRate / 1e15).toFixed(2)} PH/s`;
    if (hashRate >= 1e12) return `${(hashRate / 1e12).toFixed(2)} TH/s`;
    if (hashRate >= 1e9) return `${(hashRate / 1e9).toFixed(2)} GH/s`;
    if (hashRate >= 1e6) return `${(hashRate / 1e6).toFixed(2)} MH/s`;
    if (hashRate >= 1e3) return `${(hashRate / 1e3).toFixed(2)} KH/s`;
    return `${hashRate.toFixed(2)} H/s`;
  };

  // Chart configuration
  const chartSeries = correlationData?.aligned_data ? [
    {
      name: "Bitcoin Hash Rate",
      type: "line",
      yAxisIndex: 0,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.hash_rate
      }))
    },
    {
      name: "Bitcoin Price",
      type: "line",
      yAxisIndex: 1,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.btc_price
      }))
    }
  ] : [];

  const chartOptions: ApexOptions = {
    chart: {
      type: "line",
      height: 400,
      zoom: {
        enabled: true
      },
      toolbar: {
        show: true
      }
    },
    colors: ["#8B5CF6", "#F59E0B"], // Purple for hash rate, amber for price
    stroke: {
      width: [2, 2],
      curve: "smooth"
    },
    xaxis: {
      type: "datetime",
      labels: {
        format: "MMM yyyy"
      }
    },
    yaxis: [
      {
        title: {
          text: "Hash Rate (H/s)",
          style: {
            color: "#8B5CF6"
          }
        },
        labels: {
          style: {
            colors: "#8B5CF6"
          },
          formatter: (value: number) => formatHashRate(value)
        }
      },
      {
        opposite: true,
        title: {
          text: "Bitcoin Price (USD)",
          style: {
            color: "#F59E0B"
          }
        },
        labels: {
          style: {
            colors: "#F59E0B"
          },
          formatter: (value: number) => `$${value.toLocaleString()}`
        }
      }
    ],
    legend: {
      show: true,
      position: "top"
    },
    grid: {
      borderColor: "#E5E7EB"
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: [
        {
          formatter: (value: number) => formatHashRate(value)
        },
        {
          formatter: (value: number) => `$${value.toLocaleString()}`
        }
      ]
    }
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      {/* Header */}
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Bitcoin Hash Rate-Price Correlation Analysis
          </h3>
          <p className="mt-1 text-gray-500 text-theme-sm dark:text-gray-400">
            Statistical correlation between Bitcoin network hash rate and Bitcoin price movements
          </p>
        </div>

        {/* Period Selector */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Period:
          </label>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading correlation data...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 mb-2">⚠️</div>
            <p className="text-red-600 dark:text-red-400">{error}</p>
            <button
              onClick={fetchCorrelationData}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Content */}
      {!isLoading && !error && correlationData && (
        <>
          {/* Correlation Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Correlation Coefficient</div>
              <div
                className="text-lg font-bold"
                style={{ color: getCorrelationColor(correlationData.correlation_analysis?.correlation || null) }}
              >
                {correlationData.correlation_analysis?.correlation?.toFixed(3) || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">
                {getCorrelationDescription(correlationData.correlation_analysis?.correlation || null)}
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Data Points</div>
              <div className="text-lg font-bold text-gray-800 dark:text-white">
                {correlationData.summary.aligned_points}
              </div>
              <div className="text-sm text-gray-600">
                Aligned observations
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Latest Hash Rate</div>
              <div className="text-lg font-bold text-purple-600">
                {correlationData.summary.latest_hash_rate ? formatHashRate(correlationData.summary.latest_hash_rate) : 'N/A'}
              </div>
              <div className="text-sm text-gray-600">
                Current rate
              </div>
            </div>
          </div>

          {/* Chart */}
          <div className="max-w-full overflow-x-auto custom-scrollbar">
            <div className="min-w-[1000px] xl:min-w-full">
              <Chart
                options={chartOptions}
                series={chartSeries}
                type="line"
                height={400}
              />
            </div>
          </div>

          {/* Analysis Summary */}
          {correlationData.correlation_analysis && (
            <div className="mt-6 p-4 rounded-lg bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
              <h4 className="text-sm font-semibold text-purple-800 dark:text-purple-200 mb-2">
                Analysis Summary
              </h4>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                The correlation coefficient of <strong>{correlationData.correlation_analysis.correlation.toFixed(3)}</strong> indicates a{" "}
                <strong>{getCorrelationDescription(correlationData.correlation_analysis.correlation).toLowerCase()}</strong> relationship
                between Bitcoin network hash rate and Bitcoin prices over the selected {selectedPeriod}-day period.
                {correlationData.correlation_analysis.p_value && (
                  <> The analysis is statistically <strong>{correlationData.correlation_analysis.significance}</strong>
                  (p-value: {correlationData.correlation_analysis.p_value.toFixed(4)}).</>
                )}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default HashRateCorrelationChart;
