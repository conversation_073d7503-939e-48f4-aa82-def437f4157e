import React, { useState, useEffect } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import axios from 'axios';

interface GoldCorrelationData {
  correlation_analysis: {
    correlation: number;
    p_value?: number;
    data_points: number;
    significance: string;
  } | null;
  gold_data: Array<{
    date: string;
    price: number;
  }>;
  bitcoin_data: Array<{
    date: string;
    price: number;
  }>;
  aligned_data: Array<{
    date: string;
    gold_price: number;
    btc_price: number;
  }>;
  summary: {
    total_gold_points: number;
    total_btc_points: number;
    aligned_points: number;
    analysis_period_days: number;
    latest_gold_price: number | null;
    latest_btc_price: number | null;
  };
}

const GoldCorrelationChart: React.FC = () => {
  const [correlationData, setCorrelationData] = useState<GoldCorrelationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(365);

  const periodOptions = [
    { value: 30, label: '1 Month' },
    { value: 90, label: '3 Months' },
    { value: 180, label: '6 Months' },
    { value: 365, label: '1 Year' },
    { value: 730, label: '2 Years' },
    { value: 1095, label: '3 Years' },
    { value: 1825, label: '5 Years' },
  ];

  const fetchCorrelationData = async (days: number) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`http://localhost:8000/api/gold-correlation/?days=${days}`);
      setCorrelationData(response.data);
    } catch (err: any) {
      console.error('Error fetching gold correlation data:', err);
      setError(err.response?.data?.error || 'Failed to load gold correlation data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCorrelationData(selectedPeriod);
  }, [selectedPeriod]);

  const getCorrelationColor = (correlation: number | null): string => {
    if (correlation === null) return '#6B7280';
    if (correlation > 0.3) return '#10B981'; // Green for positive
    if (correlation < -0.3) return '#EF4444'; // Red for negative
    return '#F59E0B'; // Yellow for weak correlation
  };

  const getCorrelationDescription = (correlation: number | null): string => {
    if (correlation === null) return 'No correlation data';
    if (correlation > 0.7) return 'Strong Positive';
    if (correlation > 0.3) return 'Moderate Positive';
    if (correlation > -0.3) return 'Weak';
    if (correlation > -0.7) return 'Moderate Negative';
    return 'Strong Negative';
  };

  // Prepare chart data
  const chartSeries = correlationData ? [
    {
      name: 'Bitcoin Price',
      type: 'line',
      yAxisIndex: 0,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.btc_price
      }))
    },
    {
      name: 'Gold Price',
      type: 'line',
      yAxisIndex: 1,
      data: correlationData.aligned_data.map(point => ({
        x: new Date(point.date).getTime(),
        y: point.gold_price
      }))
    }
  ] : [];

  const chartOptions: ApexOptions = {
    chart: {
      type: 'line',
      height: 400,
      zoom: {
        enabled: true
      },
      toolbar: {
        show: true
      },
      background: 'transparent'
    },
    colors: ['#F59E0B', '#FFD700'], // Orange for Bitcoin, Gold color for Gold
    stroke: {
      width: [2, 2],
      curve: 'smooth'
    },
    xaxis: {
      type: 'datetime',
      labels: {
        style: {
          colors: '#6B7280'
        },
        format: selectedPeriod <= 365 ? 'dd MMM yyyy' : 'MMM yyyy'
      },
      axisBorder: {
        color: '#E5E7EB'
      },
      axisTicks: {
        color: '#E5E7EB'
      }
    },
    yaxis: [
      {
        title: {
          text: 'Bitcoin Price (USD)',
          style: {
            color: '#F59E0B'
          }
        },
        labels: {
          style: {
            colors: '#F59E0B'
          },
          formatter: (val: number) => `$${val.toLocaleString()}`
        },
        axisBorder: {
          show: true,
          color: '#F59E0B'
        }
      },
      {
        opposite: true,
        title: {
          text: 'Gold Price (USD)',
          style: {
            color: '#FFD700'
          }
        },
        labels: {
          style: {
            colors: '#FFD700'
          },
          formatter: (val: number) => `$${val.toLocaleString()}`
        },
        axisBorder: {
          show: true,
          color: '#FFD700'
        }
      }
    ],
    grid: {
      borderColor: '#E5E7EB',
      strokeDashArray: 3
    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      labels: {
        colors: '#6B7280'
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      x: {
        format: selectedPeriod <= 365 ? 'dd MMM yyyy' : 'MMM yyyy'
      },
      y: [
        {
          formatter: (val: number) => `$${val.toLocaleString()}`
        },
        {
          formatter: (val: number) => `$${val.toLocaleString()}`
        }
      ]
    },
    markers: {
      size: 0,
      hover: {
        size: 6,
      }
    }
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      {/* Header */}
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Bitcoin-Gold Price Correlation Analysis
          </h3>
          <p className="mt-1 text-gray-500 text-theme-sm dark:text-gray-400">
            Statistical correlation between Bitcoin and gold futures price movements
          </p>
        </div>

        {/* Period Selector */}
        <div className="flex-shrink-0">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-sm font-medium mb-2">Error loading data</div>
            <div className="text-gray-500 text-xs">{error}</div>
            <button
              onClick={() => fetchCorrelationData(selectedPeriod)}
              className="mt-3 px-4 py-2 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Content */}
      {!isLoading && !error && correlationData && (
        <>
          {/* Correlation Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Correlation Coefficient</div>
              <div
                className="text-lg font-bold"
                style={{ color: getCorrelationColor(correlationData.correlation_analysis?.correlation || null) }}
              >
                {correlationData.correlation_analysis?.correlation?.toFixed(3) || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">
                {getCorrelationDescription(correlationData.correlation_analysis?.correlation || null)}
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Data Points</div>
              <div className="text-lg font-bold text-gray-800 dark:text-white">
                {correlationData.summary.aligned_points}
              </div>
              <div className="text-sm text-gray-600">
                Aligned observations
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs font-medium text-gray-500 mb-1">Latest Gold Price</div>
              <div className="text-lg font-bold text-yellow-500">
                ${correlationData.summary.latest_gold_price?.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">
                Current price
              </div>
            </div>
          </div>

          {/* Chart */}
          <div className="max-w-full overflow-x-auto custom-scrollbar">
            <div className="min-w-[1000px] xl:min-w-full">
              <Chart
                options={chartOptions}
                series={chartSeries}
                type="line"
                height={400}
              />
            </div>
          </div>

          {/* Analysis Summary */}
          {correlationData.correlation_analysis && (
            <div className="mt-6 p-4 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800">
              <h4 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                Analysis Summary
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                The correlation coefficient of <strong>{correlationData.correlation_analysis.correlation.toFixed(3)}</strong> indicates a{" "}
                <strong>{getCorrelationDescription(correlationData.correlation_analysis.correlation).toLowerCase()}</strong> relationship
                between Bitcoin and gold prices over the selected {selectedPeriod}-day period.
                {correlationData.correlation_analysis.p_value && (
                  <> The statistical significance is <strong>{correlationData.correlation_analysis.significance}</strong>
                  (p-value: {correlationData.correlation_analysis.p_value.toFixed(4)}).</>
                )}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default GoldCorrelationChart;
