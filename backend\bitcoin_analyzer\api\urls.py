from django.urls import path
from . import views
from django.shortcuts import render

# Define the websocket_test view function if it doesn't exist
def websocket_test(request):
    return render(request, 'websocket_test.html')

urlpatterns = [
    path('test/', websocket_test, name='websocket_test'),
    path('fear-greed-index/', views.FearGreedIndexView.as_view(), name='fear-greed-index'),
    path('fear-greed/clear-cache/', views.FearGreedIndexClearCacheView.as_view(), name='fear-greed-clear-cache'),
    path('binance-klines/', views.BinanceKlinesView.as_view(), name='binance-klines'),
    path('binance-orderbook/', views.BinanceOrderBookView.as_view(), name='binance-orderbook'),
    path('bitcoin-news/', views.BitcoinNewsView.as_view(), name='bitcoin-news'),
    path('chatbot/', views.ChatbotView.as_view(), name='chatbot'),
    path('sentiment-analysis/', views.SentimentAnalysisView.as_view(), name='sentiment-analysis'),
    path('bitcoin-hash-rate/', views.BitcoinHashRateView.as_view(), name='bitcoin-hash-rate'),
    path('hash-rate-correlation/', views.HashRateCorrelationView.as_view(), name='hash-rate-correlation'),
    path('correlation-analysis/', views.CorrelationAnalysisView.as_view(), name='correlation-analysis'),
    path('m2-correlation/', views.M2CorrelationView.as_view(), name='m2-correlation'),
    path('gold-correlation/', views.GoldCorrelationView.as_view(), name='gold-correlation'),
]


